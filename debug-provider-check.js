#!/usr/bin/env node

/**
 * 完整调试Refly的provider检查流程
 */

const https = require('https');
const { URL } = require('url');

// 从命令行获取API Key
const apiKey = process.argv[2];
if (!apiKey) {
  console.log('❌ 请提供API Key作为参数');
  console.log('用法: node debug-provider-check.js YOUR_GEMINI_API_KEY');
  process.exit(1);
}

console.log('🔍 完整调试Refly Provider检查流程');
console.log('=====================================');
console.log('');

// 模拟配置
const config = {
  providerId: 'test-provider-id',
  providerKey: 'openai',  // 用户选择的OpenAI类型
  name: 'Google Gemini',
  baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
  apiKey: apiKey,
  categories: ['llm']
};

console.log('📋 配置信息:');
console.log(`  Provider Key: ${config.providerKey}`);
console.log(`  Base URL: ${config.baseUrl}`);
console.log(`  API Key: ${config.apiKey.substring(0, 10)}...`);
console.log('');

// HTTP请求函数
function makeRequest(url, options) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {}
    };
    
    const req = https.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

// 模拟getAuthHeaders方法
function getAuthHeaders(config) {
  if (!config.apiKey) return {};

  if (config.providerKey === 'anthropic') {
    return { 'x-api-key': config.apiKey };
  }

  // Check if this is a Google Gemini provider by provider key or base URL
  if (config.providerKey === 'gemini' || config.baseUrl?.includes('generativelanguage.googleapis.com')) {
    return { 'x-goog-api-key': config.apiKey };
  }

  // Default to OpenAI-style Bearer token for other providers
  return { Authorization: `Bearer ${config.apiKey}` };
}

// 模拟detectOpenRouterProvider方法
function detectOpenRouterProvider(config) {
  if (config.baseUrl?.includes('openrouter.ai')) {
    return true;
  }
  if (config.name?.toLowerCase().includes('openrouter')) {
    return true;
  }
  if (config.providerKey === 'openrouter') {
    return true;
  }
  return false;
}

// 模拟完整的checkProvider流程
async function checkProvider(config, category) {
  console.log('🔧 开始Provider检查流程');
  console.log('');
  
  const checkResult = {
    providerId: config.providerId,
    providerKey: config.providerKey,
    name: config.name,
    baseUrl: config.baseUrl,
    categories: config.categories,
    status: 'unknown',
    message: '',
    details: {},
    timestamp: new Date().toISOString(),
  };

  try {
    // 1. 路由到特定的provider检查
    console.log('1. 路由检查:');
    console.log(`   Provider Key: ${config.providerKey}`);
    
    switch (config.providerKey) {
      case 'openai':
        console.log('   → 进入OpenAI检查分支');
        
        // 检查是否是OpenRouter
        const isOpenRouter = detectOpenRouterProvider(config);
        console.log(`   → OpenRouter检测: ${isOpenRouter ? '是' : '否'}`);
        
        if (isOpenRouter) {
          console.log('   → 会走OpenRouter检查路径');
          checkResult.details = { note: 'OpenRouter provider detected' };
        } else {
          console.log('   → 会走标准LLM检查路径');
          checkResult.details = await checkLLMProvider(config, category);
        }
        break;
        
      default:
        console.log(`   → 未知的provider类型: ${config.providerKey}`);
        checkResult.details = { error: 'Unknown provider type' };
    }
    
    // 2. 评估整体状态
    console.log('');
    console.log('2. 评估整体状态:');
    
    if (checkResult.details.apiAvailability?.status === 'success') {
      checkResult.status = 'success';
      checkResult.message = 'Provider connection successful';
      console.log('   ✅ 整体状态: 成功');
    } else if (checkResult.details.apiAvailability?.status === 'failed') {
      checkResult.status = 'failed';
      checkResult.message = checkResult.details.apiAvailability.error || 'Connection check failed';
      console.log('   ❌ 整体状态: 失败');
      console.log(`   错误信息: ${checkResult.message}`);
    } else {
      checkResult.status = 'unknown';
      checkResult.message = 'Unknown status';
      console.log('   ❓ 整体状态: 未知');
    }
    
  } catch (error) {
    checkResult.status = 'failed';
    checkResult.message = error?.message || 'Connection check failed';
    checkResult.details.error = {
      status: 'failed',
      error: {
        type: error?.constructor?.name || 'Error',
        message: error?.message,
      },
    };
    console.log(`   💥 异常: ${error.message}`);
  }

  return checkResult;
}

// 模拟checkLLMProvider方法
async function checkLLMProvider(config, _category) {
  console.log('');
  console.log('🔧 执行LLM Provider检查');
  
  const checkResults = {
    apiAvailability: { status: 'unknown', error: null, data: null },
  };

  // 1. 验证API Key
  console.log('   1. 验证API Key存在性');
  if (!config.apiKey) {
    console.log('   ❌ API Key缺失');
    checkResults.apiAvailability.status = 'failed';
    checkResults.apiAvailability.error = 'API key is required';
    return checkResults;
  }
  console.log('   ✅ API Key存在');

  // 2. 获取认证头
  console.log('   2. 生成认证头');
  const authHeaders = getAuthHeaders(config);
  console.log('   认证头:');
  Object.entries(authHeaders).forEach(([key, value]) => {
    console.log(`     ${key}: ${value.substring(0, 20)}...`);
  });

  // 3. 发送API请求
  const requestUrl = `${config.baseUrl}/models`;
  console.log(`   3. 发送请求到: ${requestUrl}`);
  
  try {
    const response = await makeRequest(requestUrl, {
      method: 'GET',
      headers: authHeaders
    });
    
    console.log(`   响应状态码: ${response.statusCode}`);
    
    if (response.statusCode === 200) {
      console.log('   ✅ API请求成功');
      
      try {
        const responseData = JSON.parse(response.data);
        console.log('   4. 解析响应数据');
        
        // 新的响应格式处理逻辑
        let modelCount = 0;
        let responseFormat = 'unknown';
        
        // Check if this is a Gemini API response (has 'models' field)
        if (responseData?.models && Array.isArray(responseData.models)) {
          modelCount = responseData.models.length;
          responseFormat = 'gemini';
          console.log(`   ✅ 检测到Gemini格式响应，模型数量: ${modelCount}`);
          
          if (modelCount > 0) {
            console.log('   前3个模型:');
            responseData.models.slice(0, 3).forEach(model => {
              console.log(`     - ${model.name} (${model.displayName || 'N/A'})`);
            });
          }
        }
        // Check if this is an OpenAI API response (has 'data' field)
        else if (responseData?.data && Array.isArray(responseData.data)) {
          modelCount = responseData.data.length;
          responseFormat = 'openai';
          console.log(`   ✅ 检测到OpenAI格式响应，模型数量: ${modelCount}`);
        }
        // Handle other possible formats
        else if (Array.isArray(responseData)) {
          modelCount = responseData.length;
          responseFormat = 'array';
          console.log(`   ✅ 检测到数组格式响应，模型数量: ${modelCount}`);
        } else {
          console.log('   ❓ 未知的响应格式');
          console.log('   响应结构:');
          console.log(`     - 有data字段: ${responseData?.data ? '是' : '否'}`);
          console.log(`     - 有models字段: ${responseData?.models ? '是' : '否'}`);
          console.log(`     - 是数组: ${Array.isArray(responseData) ? '是' : '否'}`);
        }

        checkResults.apiAvailability.status = 'success';
        checkResults.apiAvailability.data = {
          statusCode: response.statusCode,
          modelCount,
          responseTime: Date.now(),
          responseFormat,
        };
        
        console.log(`   ✅ 解析成功，格式: ${responseFormat}，模型数量: ${modelCount}`);
        
      } catch (parseError) {
        console.log(`   ❌ JSON解析失败: ${parseError.message}`);
        console.log(`   原始响应: ${response.data.substring(0, 200)}...`);
        
        checkResults.apiAvailability.status = 'failed';
        checkResults.apiAvailability.error = `Failed to parse models response: ${parseError.message}`;
      }
      
    } else {
      console.log(`   ❌ API请求失败: ${response.statusCode}`);
      console.log(`   响应内容: ${response.data.substring(0, 200)}...`);
      
      checkResults.apiAvailability.status = 'failed';
      
      // 生成错误消息
      let errorMessage = `HTTP ${response.statusCode}`;
      if (response.statusCode === 401) {
        errorMessage += ': Unauthorized - Invalid API key or unauthorized access';
      } else if (response.statusCode === 403) {
        errorMessage += ': Forbidden - API access denied';
      } else if (response.statusCode === 404) {
        errorMessage += ': Not Found - API endpoint not found';
      } else {
        errorMessage += ': Request failed';
      }
      
      checkResults.apiAvailability.error = errorMessage;
    }
    
  } catch (error) {
    console.log(`   💥 请求异常: ${error.message}`);
    checkResults.apiAvailability.status = 'failed';
    checkResults.apiAvailability.error = `Request failed: ${error.message}`;
  }

  return checkResults;
}

// 主函数
async function main() {
  const result = await checkProvider(config);
  
  console.log('');
  console.log('📊 最终结果:');
  console.log('=============');
  console.log(`状态: ${result.status}`);
  console.log(`消息: ${result.message}`);
  console.log('详细信息:');
  console.log(JSON.stringify(result.details, null, 2));
  
  console.log('');
  console.log('🎯 结论:');
  if (result.status === 'success') {
    console.log('✅ Provider检查成功！Refly应该能够正常使用此配置。');
  } else {
    console.log('❌ Provider检查失败。');
    console.log('');
    console.log('🔧 可能的解决方案:');
    console.log('1. 检查API Key是否有效');
    console.log('2. 确认已启用Generative Language API服务');
    console.log('3. 检查网络连接');
    console.log('4. 验证Base URL是否正确');
  }
}

main().catch(console.error);
