#!/bin/bash

# 带Gemini自动配置的Pod启动脚本
# 启动Pod并自动配置Gemini提供商

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Pod状态
check_pod_status() {
    if podman pod exists refly-pod 2>/dev/null; then
        local status=$(podman pod ps --filter name=refly-pod --format "{{.Status}}" 2>/dev/null)
        if [[ "$status" == *"Running"* ]]; then
            return 0  # Pod正在运行
        else
            return 1  # Pod存在但未运行
        fi
    else
        return 2  # Pod不存在
    fi
}

# 启动Pod
start_pod() {
    local pod_status
    check_pod_status
    pod_status=$?
    
    case $pod_status in
        0)
            log_success "Pod已在运行中"
            return 0
            ;;
        1)
            log_info "启动现有Pod..."
            podman pod start refly-pod
            ;;
        2)
            log_info "部署新Pod..."
            if [ -f "deploy/pod-k8s/refly-simple-pod.yaml" ]; then
                podman play kube deploy/pod-k8s/refly-simple-pod.yaml
            else
                log_error "Pod配置文件不存在"
                return 1
            fi
            ;;
    esac
    
    # 等待Pod启动
    local max_wait=120
    local wait_time=0
    local check_interval=5
    
    while [ $wait_time -lt $max_wait ]; do
        check_pod_status
        if [ $? -eq 0 ]; then
            log_success "Pod启动成功"
            return 0
        fi
        
        sleep $check_interval
        wait_time=$((wait_time + check_interval))
        log_info "等待Pod启动... (${wait_time}s/${max_wait}s)"
    done
    
    log_error "Pod启动超时"
    return 1
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    local services=("5700:Web" "5800:API")
    local all_ready=false
    local max_attempts=24  # 2分钟
    local attempt=0
    
    while [ $attempt -lt $max_attempts ] && [ "$all_ready" = false ]; do
        all_ready=true
        
        for service in "${services[@]}"; do
            IFS=':' read -r port name <<< "$service"
            if ! curl -s -f "http://localhost:$port" > /dev/null 2>&1; then
                all_ready=false
                break
            fi
        done
        
        if [ "$all_ready" = true ]; then
            log_success "所有服务已就绪"
            return 0
        fi
        
        sleep 5
        attempt=$((attempt + 1))
        log_info "等待服务就绪... (${attempt}/${max_attempts})"
    done
    
    log_error "服务启动超时"
    return 1
}

# 运行Gemini自动配置
run_gemini_auto_config() {
    log_info "运行Gemini自动配置..."
    
    if [ -f "deploy/pod-k8s/auto-configure-gemini.sh" ]; then
        # 在后台运行，避免阻塞
        (
            sleep 10  # 给服务更多时间完全启动
            bash deploy/pod-k8s/auto-configure-gemini.sh
        ) &
        
        log_success "Gemini自动配置已启动（后台运行）"
        return 0
    else
        log_warning "Gemini自动配置脚本不存在，跳过"
        return 1
    fi
}

# 显示状态信息
show_status() {
    log_info "系统状态:"
    echo ""
    
    # Pod状态
    echo "📦 Pod状态:"
    podman pod ps --filter name=refly-pod
    echo ""
    
    # 容器状态
    echo "🐳 容器状态:"
    podman ps --filter pod=refly-pod --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    echo ""
    
    # 服务访问信息
    echo "🌐 服务访问:"
    echo "   Web界面: http://localhost:5700"
    echo "   API服务: http://localhost:5800"
    echo "   MinIO控制台: http://localhost:39001"
    echo "   Redis Insight: http://localhost:38001"
    echo ""
    
    # Gemini配置状态
    if [ -f "/tmp/refly-pod/gemini-auto-config.status" ]; then
        echo "🤖 Gemini配置状态:"
        source "/tmp/refly-pod/gemini-auto-config.status" 2>/dev/null || true
        if [ "$CONFIG_SUCCESS" = "true" ]; then
            echo "   ✅ 自动配置成功"
            echo "   📅 配置时间: $LAST_CONFIG_TIME"
            echo "   🏷️  提供商名称: $GEMINI_PROVIDER_NAME"
        else
            echo "   ❌ 自动配置失败"
        fi
    else
        echo "🤖 Gemini配置状态:"
        echo "   ⏳ 配置中或未启用"
    fi
    echo ""
}

# 创建systemd服务（可选）
create_systemd_service() {
    if [ "$1" = "--create-service" ]; then
        log_info "创建systemd服务..."
        
        local service_file="/etc/systemd/system/refly-pod.service"
        local script_path="$(realpath "$0")"
        local work_dir="$(pwd)"
        
        cat > "$service_file" << EOF
[Unit]
Description=Refly Pod with Gemini Auto-Configuration
After=network.target

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=$work_dir
ExecStart=$script_path
ExecStop=/usr/bin/podman pod stop refly-pod
User=$(whoami)
Group=$(id -gn)

[Install]
WantedBy=multi-user.target
EOF
        
        systemctl daemon-reload
        systemctl enable refly-pod.service
        
        log_success "systemd服务已创建并启用"
        log_info "使用以下命令管理服务:"
        log_info "  启动: sudo systemctl start refly-pod"
        log_info "  停止: sudo systemctl stop refly-pod"
        log_info "  状态: sudo systemctl status refly-pod"
    fi
}

# 显示帮助信息
show_help() {
    echo "Refly Pod 启动脚本（带Gemini自动配置）"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --help              显示此帮助信息"
    echo "  --create-service    创建systemd服务"
    echo "  --no-gemini         跳过Gemini自动配置"
    echo "  --status-only       仅显示状态信息"
    echo ""
    echo "环境变量:"
    echo "  SKIP_GEMINI_CONFIG  设置为'true'跳过Gemini配置"
    echo ""
    echo "配置文件:"
    echo "  deploy/pod-k8s/gemini-auto-config.env"
    echo ""
}

# 主函数
main() {
    local skip_gemini=false
    local status_only=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help)
                show_help
                exit 0
                ;;
            --create-service)
                create_systemd_service --create-service
                exit 0
                ;;
            --no-gemini)
                skip_gemini=true
                shift
                ;;
            --status-only)
                status_only=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查环境变量
    if [ "$SKIP_GEMINI_CONFIG" = "true" ]; then
        skip_gemini=true
    fi
    
    echo "🚀 Refly Pod 启动脚本（带Gemini自动配置）"
    echo "============================================"
    echo ""
    
    # 仅显示状态
    if [ "$status_only" = true ]; then
        show_status
        exit 0
    fi
    
    # 启动Pod
    if ! start_pod; then
        log_error "Pod启动失败"
        exit 1
    fi
    
    # 等待服务就绪
    if ! wait_for_services; then
        log_warning "部分服务可能未完全启动，但继续执行"
    fi
    
    # 运行Gemini自动配置
    if [ "$skip_gemini" = false ]; then
        run_gemini_auto_config
    else
        log_info "跳过Gemini自动配置"
    fi
    
    # 显示状态
    show_status
    
    log_success "🎉 启动完成！"
    echo ""
    log_info "💡 提示:"
    echo "  - 访问Web界面: http://localhost:5700"
    echo "  - 查看Gemini配置: 设置 -> 提供商"
    echo "  - 管理Pod: ./deploy/pod-k8s/manage-refly-pod.sh"
    echo "  - 如需配置Gemini，请编辑: deploy/pod-k8s/gemini-auto-config.env"
}

# 运行主函数
main "$@"
