#!/bin/bash

# Gemini API 401错误诊断脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🔍 Gemini API 401错误诊断工具"
echo "================================"
echo ""

# 获取API Key
read -p "请输入您的Gemini API Key: " -s api_key
echo ""

if [ -z "$api_key" ]; then
    log_error "API Key不能为空"
    exit 1
fi

echo ""
log_info "开始诊断..."

# 1. 检查API Key格式
echo ""
log_info "1. 检查API Key格式"
if [[ "$api_key" =~ ^AIza ]]; then
    log_success "✅ API Key格式正确（以AIza开头）"
else
    log_warning "⚠️  API Key格式可能不正确（应以AIza开头）"
    echo "   当前格式: ${api_key:0:10}..."
fi

# 2. 测试网络连接
echo ""
log_info "2. 测试网络连接"
if curl -s -I https://generativelanguage.googleapis.com/v1beta/models > /dev/null; then
    log_success "✅ 网络连接正常"
else
    log_error "❌ 无法连接到Google API"
    exit 1
fi

# 3. 测试API Key有效性
echo ""
log_info "3. 测试API Key有效性"

# 使用正确的认证头测试
response=$(curl -s -w "%{http_code}" \
    -H "x-goog-api-key: $api_key" \
    "https://generativelanguage.googleapis.com/v1beta/models" \
    -o /tmp/gemini_test.json)

echo "   HTTP状态码: $response"

case $response in
    200)
        log_success "✅ API Key有效！"
        model_count=$(jq -r '.models | length' /tmp/gemini_test.json 2>/dev/null || echo "0")
        log_info "   可用模型数量: $model_count"
        
        if [ "$model_count" -gt 0 ]; then
            echo "   前3个可用模型:"
            jq -r '.models[0:3][] | "     - \(.name) (\(.displayName))"' /tmp/gemini_test.json 2>/dev/null || echo "     无法解析模型列表"
        fi
        ;;
    401)
        log_error "❌ API Key无效或未授权"
        echo ""
        log_info "可能的解决方案:"
        echo "   1. 检查API Key是否正确复制"
        echo "   2. 确认API Key未过期"
        echo "   3. 检查Google Cloud项目状态"
        
        # 显示详细错误信息
        if [ -f /tmp/gemini_test.json ]; then
            echo ""
            log_info "详细错误信息:"
            cat /tmp/gemini_test.json | jq . 2>/dev/null || cat /tmp/gemini_test.json
        fi
        ;;
    403)
        log_error "❌ API访问被禁止"
        echo ""
        log_info "可能的解决方案:"
        echo "   1. 启用Generative Language API服务"
        echo "   2. 检查API Key权限设置"
        echo "   3. 验证Google Cloud项目计费状态"
        ;;
    429)
        log_warning "⚠️  API配额已用完或请求过于频繁"
        echo ""
        log_info "解决方案:"
        echo "   1. 等待一段时间后重试"
        echo "   2. 检查Google Cloud Console中的配额设置"
        ;;
    *)
        log_error "❌ 未知错误，状态码: $response"
        if [ -f /tmp/gemini_test.json ]; then
            echo ""
            log_info "响应内容:"
            cat /tmp/gemini_test.json
        fi
        ;;
esac

# 4. 测试使用错误认证头的情况
echo ""
log_info "4. 测试错误认证方式（用于对比）"

wrong_response=$(curl -s -w "%{http_code}" \
    -H "Authorization: Bearer $api_key" \
    "https://generativelanguage.googleapis.com/v1beta/models" \
    -o /tmp/gemini_wrong.json)

echo "   使用Authorization Bearer的状态码: $wrong_response"

if [ "$wrong_response" = "401" ]; then
    log_success "✅ 确认需要使用x-goog-api-key认证（这是正确的）"
else
    log_warning "⚠️  意外：Authorization Bearer也返回了$wrong_response"
fi

# 5. 检查Refly的检测逻辑
echo ""
log_info "5. 验证Refly检测逻辑"

base_url="https://generativelanguage.googleapis.com/v1beta"
if [[ "$base_url" == *"generativelanguage.googleapis.com"* ]]; then
    log_success "✅ Refly应该正确检测到Gemini API"
    log_success "✅ 应该使用x-goog-api-key认证头"
else
    log_error "❌ Refly检测逻辑可能有问题"
fi

# 6. 生成解决方案
echo ""
echo "🎯 解决方案建议"
echo "================"

if [ "$response" = "200" ]; then
    echo ""
    log_success "🎉 您的API Key是有效的！"
    echo ""
    log_info "问题可能在于Refly的配置，请尝试:"
    echo "   1. 重启API服务: podman restart refly-pod-api"
    echo "   2. 清除浏览器缓存"
    echo "   3. 重新测试连接"
    echo ""
    log_info "如果问题仍然存在，可能是Refly内部的认证逻辑问题"
    
elif [ "$response" = "401" ]; then
    echo ""
    log_error "🚨 API Key认证失败"
    echo ""
    echo "📋 检查清单:"
    echo "   □ API Key是否完整复制（包括所有字符）"
    echo "   □ API Key是否以'AIza'开头"
    echo "   □ Google Cloud项目是否启用了Generative Language API"
    echo "   □ API Key是否有IP限制"
    echo "   □ Google Cloud项目是否有有效的计费账户"
    echo ""
    echo "🔗 有用链接:"
    echo "   • 获取API Key: https://ai.google.dev/gemini-api/docs/api-key"
    echo "   • 启用API服务: https://console.cloud.google.com/apis/library/generativelanguage.googleapis.com"
    echo "   • 检查配额: https://console.cloud.google.com/iam-admin/quotas"
    
elif [ "$response" = "403" ]; then
    echo ""
    log_error "🚨 API服务未启用或权限不足"
    echo ""
    echo "📋 必须执行的步骤:"
    echo "   1. 访问: https://console.cloud.google.com/apis/library/generativelanguage.googleapis.com"
    echo "   2. 点击'启用'按钮"
    echo "   3. 确保项目有有效的计费账户"
    echo "   4. 等待几分钟让服务生效"
fi

# 7. 提供测试命令
echo ""
echo "🧪 手动测试命令"
echo "==============="
echo ""
echo "您可以使用以下命令手动测试API Key:"
echo ""
echo "curl -H \"x-goog-api-key: YOUR_API_KEY\" \\"
echo "  \"https://generativelanguage.googleapis.com/v1beta/models\""
echo ""

# 清理临时文件
rm -f /tmp/gemini_test.json /tmp/gemini_wrong.json

echo ""
log_info "诊断完成！"
